<?php

use PhpOffice\PhpSpreadsheet\Writer\Ods\Thumbnails;
use Spipu\Html2Pdf\Tag\Html\Em;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsLearningsMaterial $mslearningmaterial
 * @property MsCourse $mscourse
 * @property MsClass $msclass
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 * @property CI_Session $session
 */
class LearningMaterialController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsCourse', 'mscourse');
        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsLearningMaterial', 'mslearningmaterial');
        $this->load->model('MsLearningMaterialDetail', 'mslearningmaterialdetail');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Bahan Belajar';
        $data['content'] = 'admin/master/learningmaterial/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsLearningMaterial', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        $classid = getPost('classid', null);

        if ($classid != null) {
            $where['b.classid'] = $classid;
        }

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] =
                '<img src="' . base_url('uploads/learning/thumbnail/' . $value->thumbnail) . '" style="width: 150px; height: 150px; object-fit: cover; object-position: center;">';
            $detail[] = $value->title;
            $detail[] = $value->type;
            $detail[] = $value->classname;
            $detail[] = $value->coursename;
            $detail[] = $value->description;
            if (strtolower(trim($value->type)) === 'video') {
                $detail[] =
                    '<a href="' . htmlspecialchars($value->link) . '" target="_blank">'
                    . htmlspecialchars($value->link)
                    . '</a>';
            } else {
                $detail[] = '<a href="' . base_url('uploads/learning/document/' . $value->document) . '" download>' . basename($value->document) . '</a>';
            }
            $actions = "";
            if (isAdmin()) {
                $actions .= " <div class=\"d-flex\"><a href=\"" . base_url('master/learningmaterial/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                                                <i class=\"ti ti-edit\"></i>
                                            </a>
                                            <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-2\" onclick=\"deleteLearningMaterial('" . $value->id . "','" . $value->title . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                                                <i class=\"ti ti-trash\"></i>
                                            </button>
                                            </div>";
            }
            if (!empty($value->document) && file_exists('./uploads/learning/document/' . $value->document)) {
                $actiondownload = "   
                    <a href=\"" . base_url('uploads/learning/document/' . $value->document) . "\" class=\"btn btn-success btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" download title=\"Download Document\">
                        <i class=\"ti ti-download\"></i>
                    </a>";
            } else {
                $actiondownload = '';
            }

            $actions .= "<div class=\"d-flex\">" . $actiondownload . "</div>";
            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['course'] = $this->mscourse->order_by('name', 'asc')->get(array(
            'createdby' => getCurrentIdUser(),
        ))->result();
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();
        $data['title'] = 'Bahan Belajar - Tambah Bahan Belajar';
        $data['content'] = 'admin/master/learningmaterial/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $title = trim(getPost('title') ?? '');
            $courseid = getPost('course');
            $classid = getPost('classid');
            $type = getPost('type');
            $description = getPost('description');
            $thumbnail = $_FILES['thumbnail'] ?? null;

            if (empty($title)) {
                throw new Exception('Judul tidak boleh kosong');
            } else if (empty($courseid)) {
                throw new Exception('Mata Pelajaran tidak boleh kosong');
            } else if (empty($classid) || count($classid) == 0) {
                throw new Exception('Kelas tidak boleh kosong');
            } else if (empty($type)) {
                throw new Exception('Jenis tidak boleh kosong');
            } else if (empty(trim($description))) {
                throw new Exception('Deskripsi tidak boleh kosong');
            }

            $course_exist = $this->mscourse->get(array('id' => $courseid))->num_rows();
            if ($course_exist == 0) {
                throw new Exception('Mata pelajaran tidak valid');
            }

            $getclass = $this->msclass->select('id')->where_in('id', $classid)->where('isdeleted', null)->get()->result();
            if (count($getclass) == 0) {
                throw new Exception('Kelas tidak valid');
            }

            $this->load->library('upload');
            $thumbnail_path = null;
            $document_path = null;
            $link_path = null;

            if (!empty($thumbnail) && $thumbnail['size'] > 0) {
                $upload_config = [
                    'upload_path'   => './uploads/learning/thumbnail',
                    'allowed_types' => 'jpg|jpeg|png|webp',
                    'max_size'      => 5048,
                    'encrypt_name'  => true,
                ];

                $this->upload->initialize($upload_config);
                if (!$this->upload->do_upload('thumbnail')) {
                    throw new Exception('Gagal mengupload thumbnail');
                }

                $thumbnail_path = $this->upload->data('file_name');
            }

            if ($type === 'ebook') {
                if (isset($_FILES['document']) && $_FILES['document']['size'] > 0) {
                    if ($_FILES['document']['size'] > 10485760) { // 10MB
                        throw new Exception('Ukuran file maksimal 10MB');
                    }

                    $upload_config = [
                        'upload_path'   => './uploads/learning/document/', // Pastikan folder ini ada
                        'allowed_types' => 'pdf|doc|docx|ppt|pptx|txt',
                        'max_size'      => 10240, // 10MB
                        'encrypt_name'  => true
                    ];

                    $this->upload->initialize($upload_config);

                    if (!$this->upload->do_upload('document')) {
                        throw new Exception('Gagal mengupload dokumen!');
                    }

                    $document_path = $this->upload->data('file_name');
                    $link_path = null;
                } else {
                    throw new Exception('Dokumen tidak boleh kosong');
                }
            } elseif ($type === 'video') {
                $link_path = getPost('url');
                if (empty(trim($link_path))) {
                    throw new Exception('URL video tidak boleh kosong');
                }
                $document_path = null;
            } else {
                throw new Exception('Tipe tidak valid');
            }

            $insert = array();
            $insert['title'] = $title;
            $insert['courseid'] = $courseid;
            $insert['type'] = $type;
            $insert['description'] = $description;
            $insert['document'] = $document_path;
            $insert['link'] = $link_path;
            $insert['thumbnail'] = $thumbnail_path;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->mslearningmaterial->insert($insert);

            $learningmaterialid = $this->db->insert_id();

            $insert_batch = array();
            foreach ($getclass as $value) {
                $insert_batch[] = array(
                    'learningmaterialid' => $learningmaterialid,
                    'classid' => $value->id,
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser()
                );
            }
            $this->mslearningmaterialdetail->insert_batch($insert_batch);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data berhasil disimpan');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $learningmaterial = $this->mslearningmaterial->get(array('id' => $id));

        if ($learningmaterial->num_rows() == 0) {
            return redirect(base_url('master/learningmaterial'));
        }

        $data = array();
        $data['title'] = 'Bahan Belajar - Ubah Bahan Belajar';
        $data['content'] = 'admin/master/learningmaterial/edit';
        $data['course'] = $this->mscourse->order_by('name', 'asc')->get(array(
            'createdby' => getCurrentIdUser(),
        ))->result();
        $data['learning'] = $learningmaterial->row();
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();
        $data['selectedclass'] = $this->mslearningmaterialdetail->select('a.learningmaterialid, a.classid')
            ->where(array('a.learningmaterialid' => $id))
            ->group_by(array('a.learningmaterialid', 'a.classid'))
            ->get()
            ->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            // Fetch the learning material for this user
            $learningmaterial = $this->mslearningmaterial->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

            if ($learningmaterial->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $learning = $learningmaterial->row();

            // Get input data
            $title = getPost('title');
            $courseid = getPost('course');
            $classid = getPost('classid');
            $type = getPost('type');
            $description = getPost('description');
            $thumbnail = $_FILES['thumbnail'];

            // Validate inputs
            if (empty(trim($title))) {
                throw new Exception('Judul tidak boleh kosong');
            } elseif (empty($courseid)) {
                throw new Exception('Mata pelajaran tidak boleh kosong');
            } elseif (empty($classid) || count($classid) == 0) {
                throw new Exception('Kelas tidak boleh kosong');
            } elseif (empty($type)) {
                throw new Exception('Tipe tidak boleh kosong');
            } elseif (empty(trim($description))) {
                throw new Exception('Deskripsi tidak boleh kosong');
            }

            // Validate course
            $course_exist = $this->mscourse->get(array('id' => $courseid))->num_rows();
            if ($course_exist == 0) {
                throw new Exception('Mata pelajaran tidak valid');
            }


            $classid = (array)$classid;
            if (count($classid) == 0) {
                throw new Exception('Kelas tidak valid');
            }

            // Fetch classes from the database
            $getclass = $this->msclass->select('id')->where_in('id', $classid)->where('isdeleted', null)->get()->result();
            if (count($getclass) == 0) {
                throw new Exception('Kelas tidak valid');
            }

            // Start uploading logic for thumbnail, document, and URL
            $this->load->library('upload');
            $thumbnail_path = $learning->thumbnail;
            $document_path = $learning->document;
            $link_path = $learning->link;

            // Handle thumbnail upload
            if (!empty($thumbnail) && $thumbnail['size'] > 0) {
                // Create directory if it doesn't exist
                if (!is_dir('./uploads/learning/thumbnail/')) {
                    mkdir('./uploads/learning/thumbnail/', 0777, true);
                }

                $upload_config = [
                    'upload_path'   => './uploads/learning/thumbnail/',
                    'allowed_types' => 'jpg|jpeg|png|webp',
                    'max_size'      => 5048,
                    'encrypt_name'     => true,
                ];

                $this->upload->initialize($upload_config);
                if (!$this->upload->do_upload('thumbnail')) {
                    $error = $this->upload->display_errors('', '');
                    throw new Exception('Gagal mengupload thumbnail: ' . $error);
                }

                // Remove old thumbnail if exists
                if (!empty($learning->thumbnail) && file_exists('./uploads/learning/thumbnail/' . $learning->thumbnail)) {
                    unlink('./uploads/learning/thumbnail/' . $learning->thumbnail);
                }

                $thumbnail_path = $this->upload->data('file_name');
            }

            // Handle document upload (for ebooks)
            if ($type === 'ebook') {
                if (isset($_FILES['document']) && $_FILES['document']['size'] > 0) {
                    if ($_FILES['document']['size'] > 10485760) { // 10MB
                        throw new Exception('Ukuran file maksimal 10MB');
                    }

                    // Create directory if it doesn't exist
                    if (!is_dir('./uploads/learning/document/')) {
                        mkdir('./uploads/learning/document/', 0777, true);
                    }

                    $upload_config = [
                        'upload_path'   => './uploads/learning/document/',
                        'allowed_types' => 'pdf|doc|docx|ppt|pptx|txt',
                        'max_size'      => 10240,
                        'encrypt_name'  => true,
                    ];

                    $this->upload->initialize($upload_config);
                    if (!$this->upload->do_upload('document')) {
                        $error = $this->upload->display_errors('', '');
                        throw new Exception('Gagal mengupload dokumen: ' . $error);
                    }

                    // Remove old document if exists
                    if (!empty($learning->document) && file_exists('./uploads/learning/document/' . $learning->document)) {
                        unlink('./uploads/learning/document/' . $learning->document);
                    }

                    $document_path = $this->upload->data('file_name');
                    $link_path = null; // Reset URL if document is uploaded
                }
            } elseif ($type === 'video') {
                $link_path = getPost('url');
                if (empty(trim($link_path))) {
                    throw new Exception('URL video tidak boleh kosong');
                }

                // Remove old document if it was an ebook
                if ($learning->type === 'ebook' && !empty($learning->document) && file_exists('./uploads/learning/document/' . $learning->document)) {
                    unlink('./uploads/learning/document/' . $learning->document);
                }

                $document_path = null;
            } else {
                throw new Exception('Tipe tidak valid');
            }

            // Prepare data for updating learning material
            $update = array(
                'title' => $title,
                'courseid' => $courseid,
                'type' => $type,
                'description' => $description,
                'document' => $document_path,
                'link' => $link_path,
                'thumbnail' => $thumbnail_path,
                'updateddate' => getCurrentDate(),
                'updatedby' => getCurrentIdUser(),
            );

            $this->mslearningmaterial->update(array('id' => $id), $update);

            $insert_batch = array();
            $update_batch = array();
            $listclass = array();

            foreach ($getclass as $value) {
                $cek = false;
                $learningmaterialdetail = $this->mslearningmaterialdetail->get(array('learningmaterialid' => $id, 'classid' => $value->id));

                if ($learningmaterialdetail->num_rows() > 0) {
                    $cek = true;
                    $listclass[] = $value->id;
                    $update_batch[] = array(
                        'id' => $learningmaterialdetail->row()->id,
                        'learningmaterialid' => $id,
                        'classid' => $value->id,
                        'updateddate' => getCurrentDate(),
                        'updatedby' => getCurrentIdUser(),
                    );
                } else {
                    $insert_batch[] = array(
                        'learningmaterialid' => $id,
                        'classid' => $value->id,
                        'createddate' => getCurrentDate(),
                        'createdby' => getCurrentIdUser(),
                    );
                }
            }

            $delete = array();
            $learningmaterialdetails = $this->mslearningmaterialdetail->get(array('learningmaterialid' => $id));

            foreach ($learningmaterialdetails->result() as $value) {
                if (!in_array($value->classid, $listclass)) {
                    $delete[] = $value->classid;
                }
            }

            if (count($delete) > 0) {
                $this->mslearningmaterialdetail->where('learningmaterialid', $id)
                    ->where_in('classid', $delete)
                    ->delete();
            }

            $insert_batch_chunk = array_chunk($insert_batch, 100);
            $update_batch_chunk = array_chunk($update_batch, 100);

            foreach ($insert_batch_chunk as $value) {
                $this->mslearningmaterialdetail->insert_batch($value);
            }

            foreach ($update_batch_chunk as $value) {
                $this->mslearningmaterialdetail->update_batch($value, 'id');
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan perubahan');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data Bahan Belajar berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get = $this->mslearningmaterial->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();
        $document = $row->document;
        $thumbnail = $row->thumbnail;

        // Hapus file sertifikat jika ada
        if (!empty($document)) {
            $documentPath = './uploads/learning/document/' . $document;
            if (file_exists($documentPath)) {
                unlink($documentPath);
            }
        }

        // Hapus file foto jika ada
        if (!empty($thumbnail)) {
            $thumbnailPath = './uploads/learning/thumbnail/' . $thumbnail;
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
        }

        $this->mslearningmaterialdetail->delete(array(
            'learningmaterialid' => $id
        ));

        // Hapus data dari database
        $delete = $this->mslearningmaterial->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data Bahan Belajar berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data Bahan Belajar gagal dihapus');
        }
    }
}
